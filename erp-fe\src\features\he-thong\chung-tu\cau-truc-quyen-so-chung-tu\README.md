# Cấu trúc quyền số chứng từ - Data Mapping Update

## Tổng quan

Cập nhật cách map dữ liệu từ API `/entities/${entity.slug}/erp/${QUERY_KEYS.QUYEN_CHUNG_TU}/` cho tab "Chứng từ & người sử dụng" trong trang cấu trúc quyền số chứng từ.

## Thay đổi chính

### 1. Cập nhật Data Mapping từ API

**Trước đây**: Sử dụng `danh_sach_chung_tu` array
**Hiện tại**: Sử dụng `chi_tiet[]` array từ API response

### 2. Field Mapping

Các field được map theo yêu cầu:

| Cột hiển thị | Field trong chi_tiet[] | Mô tả |
|--------------|------------------------|-------|
| <PERSON>ã chứng từ | `chi_tiet[].ma_ct` | Mã chứng từ |
| Tên chứng từ | `chi_tiet[].ten_ct` | Tên chứng từ |
| Người sử dụng nhóm | `chi_tiet[].username` | Username của người sử dụng |
| Tên người sử dụng | `chi_tiet[].user_id_data.nickname` hoặc `chi_tiet[].user_id_data.first_name` | Tên hiển thị của người sử dụng |

### 3. Cấu trúc dữ liệu API

```typescript
interface QuyenChungTuChiTiet {
  uuid: string;
  ma_ct: string;           // Mã chứng từ
  ten_ct: string;          // Tên chứng từ  
  username: string;        // Username
  user_id: number;         // ID người dùng
  user_id_data: {          // Thông tin chi tiết người dùng
    nickname?: string;     // Tên hiển thị ưu tiên
    first_name: string;    // Tên dự phòng
    email?: string;
  };
  chung_tu_uuid: string;   // UUID của chứng từ
  line: number;            // Số thứ tự
}
```

## Files đã thay đổi

### 1. `components/dialog/index.tsx`

- **Cập nhật useEffect**: Thay đổi từ `initialData.danh_sach_chung_tu` sang `initialData.chi_tiet`
- **Field mapping**: Cập nhật cách map các field từ API response
- **Data storage**: Cập nhật cách lưu trữ `selectedChungTuData` và `selectedUserData`
- **Submit format**: Cập nhật format dữ liệu khi submit

### 2. `components/dialog/InputTableTab/cols.tsx`

- **Column field**: Cập nhật field name cho cột "Người sử dụng Nhóm" từ `user_id` sang `username`
- **Column field**: Cập nhật field name cho cột "Tên người sử dụng" từ `username` sang `first_name`

## Backward Compatibility

Vẫn giữ logic fallback để xử lý `danh_sach_chung_tu` nếu `chi_tiet` không có sẵn, đảm bảo tương thích ngược.

## Testing

Để test các thay đổi:

1. Mở trang `/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/`
2. Chọn một record và click "Sửa"
3. Chuyển sang tab "Chứng từ & người sử dụng"
4. Kiểm tra dữ liệu hiển thị đúng theo mapping mới
5. Thêm/sửa dữ liệu và kiểm tra submit

## API Response Example

```json
{
  "uuid": "...",
  "ma_nk": "...",
  "ten_nk": "...",
  "chi_tiet": [
    {
      "uuid": "...",
      "ma_ct": "PC",
      "ten_ct": "Phiếu chi",
      "username": "admin",
      "user_id": 1,
      "user_id_data": {
        "nickname": "Administrator",
        "first_name": "Admin",
        "email": "<EMAIL>"
      },
      "chung_tu_uuid": "...",
      "line": 1
    }
  ]
}
```
